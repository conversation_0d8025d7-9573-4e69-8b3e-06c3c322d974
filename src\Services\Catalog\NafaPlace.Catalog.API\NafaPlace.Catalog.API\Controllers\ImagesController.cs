using Microsoft.AspNetCore.Mvc;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/images")]
    public class ImagesController : ControllerBase
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string _productImagesContainer;
        private readonly string _categoryImagesContainer;
        private readonly IWebHostEnvironment _environment;

        public ImagesController(BlobServiceClient blobServiceClient, IConfiguration configuration, IWebHostEnvironment environment)
        {
            _blobServiceClient = blobServiceClient;
            _productImagesContainer = configuration["Storage:ProductImagesContainer"] ?? "product-images";
            _categoryImagesContainer = configuration["Storage:CategoryImagesContainer"] ?? "category-images";
            _environment = environment;
        }

        [HttpGet("{*imagePath}")]
        public async Task<IActionResult> GetImage(string imagePath)
        {
            try
            {
                // Nettoyer le chemin de l'image
                if (string.IsNullOrEmpty(imagePath))
                {
                    return NotFound();
                }

                // Déterminer le conteneur basé sur le chemin
                string containerName;
                string actualImagePath;

                if (imagePath.StartsWith("categories/"))
                {
                    containerName = _categoryImagesContainer;
                    actualImagePath = imagePath.Substring("categories/".Length);
                }
                else
                {
                    containerName = _productImagesContainer;
                    actualImagePath = imagePath;
                }

                // Obtenir le conteneur
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);

                // Obtenir le blob client
                var blobClient = containerClient.GetBlobClient(actualImagePath);

                // Vérifier si le blob existe
                var exists = await blobClient.ExistsAsync();
                if (!exists.Value)
                {
                    return NotFound();
                }

                // Télécharger le blob
                var response = await blobClient.DownloadAsync();

                // Déterminer le type de contenu
                var contentType = "image/jpeg"; // Par défaut
                if (actualImagePath.EndsWith(".png", StringComparison.OrdinalIgnoreCase))
                    contentType = "image/png";
                else if (actualImagePath.EndsWith(".gif", StringComparison.OrdinalIgnoreCase))
                    contentType = "image/gif";
                else if (actualImagePath.EndsWith(".webp", StringComparison.OrdinalIgnoreCase))
                    contentType = "image/webp";

                // Retourner l'image
                return File(response.Value.Content, contentType);
            }
            catch (Exception ex)
            {
                // Log l'erreur (vous pouvez ajouter un logger ici)
                Console.WriteLine($"Erreur lors de la récupération de l'image {imagePath}: {ex.Message}");
                return NotFound();
            }
        }

        [HttpGet("placeholder")]
        public IActionResult GetPlaceholder()
        {
            // Retourner une image placeholder simple (1x1 pixel transparent)
            var placeholderBytes = Convert.FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==");
            return File(placeholderBytes, "image/png");
        }
    }
}
