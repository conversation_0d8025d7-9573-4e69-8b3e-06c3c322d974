using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using NafaPlace.AdminPortal;
using NafaPlace.AdminPortal.Components;
using NafaPlace.AdminPortal.Models;
using NafaPlace.AdminPortal.Services;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using Microsoft.JSInterop;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configuration des API
var apiSettings = builder.Configuration.GetSection("ApiSettings").Get<ApiSettings>();

// Configuration de l'HttpClient pour l'application
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Configuration du client HTTP pour l'API Catalog
builder.Services.AddScoped<CategoryService>(sp => 
{
    var catalogApiUrl = apiSettings?.CatalogApiUrl ?? "http://localhost:5243";
    Console.WriteLine($"Configuring CategoryService with URL: {catalogApiUrl}");
    
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    
    return new CategoryService(httpClient);
});

// Configuration du client HTTP pour l'API Identity
builder.Services.AddScoped<IAuthService>(sp => 
{
    var identityApiUrl = apiSettings?.IdentityApiUrl ?? "http://localhost:5155";
    Console.WriteLine($"Configuring AuthService with URL: {identityApiUrl}");
    
    var httpClient = new HttpClient { BaseAddress = new Uri(identityApiUrl) };
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var authStateProvider = sp.GetRequiredService<CustomAuthStateProvider>();
    
    return new AuthService(httpClient, localStorage, authStateProvider);
});

// Ajout du service LocalStorage
builder.Services.AddBlazoredLocalStorage();

// Configuration du service de produits
builder.Services.AddScoped<ProductService>(sp => 
{
    var catalogApiUrl = apiSettings?.CatalogApiUrl ?? "http://localhost:5243";
    Console.WriteLine($"Configuring ProductService with URL: {catalogApiUrl}");
    
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    
    return new ProductService(httpClient);
});

// Configuration du service d'images
builder.Services.AddScoped<ImageService>(sp => 
{
    var catalogApiUrl = apiSettings?.CatalogApiUrl ?? "http://localhost:5243";
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    
    return new ImageService(httpClient, jsRuntime);
});

// Configuration du service de notifications
builder.Services.AddScoped<NotificationService>();

// Configuration du service de gestion des utilisateurs
builder.Services.AddScoped<IUserService>(sp =>
{
    var identityApiUrl = apiSettings?.IdentityApiUrl ?? "http://localhost:5155";
    Console.WriteLine($"Configuring UserService with URL: {identityApiUrl}");

    var httpClient = new HttpClient { BaseAddress = new Uri(identityApiUrl) };
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    return new UserService(httpClient, localStorage);
});

// Configuration du service de gestion des commandes
builder.Services.AddScoped<IOrderService>(sp =>
{
    var orderApiUrl = apiSettings?.OrderApiUrl ?? "http://localhost:5004";
    Console.WriteLine($"Configuring OrderService with URL: {orderApiUrl}");

    var httpClient = new HttpClient { BaseAddress = new Uri(orderApiUrl) };
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

    var authService = sp.GetRequiredService<IAuthService>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();

    return new OrderService(httpClient, authService, localStorage);
});

// Configuration du service de gestion des avis
builder.Services.AddScoped<IReviewService>(sp =>
{
    var reviewApiUrl = apiSettings?.ReviewApiUrl ?? "http://localhost:5006";
    Console.WriteLine($"Configuring ReviewService with URL: {reviewApiUrl}");

    var httpClient = new HttpClient { BaseAddress = new Uri(reviewApiUrl) };
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    return new ReviewService(httpClient, localStorage);
});

// Configuration de l'authentification
builder.Services.AddScoped<CustomAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthStateProvider>());
builder.Services.AddAuthorizationCore();

await builder.Build().RunAsync();
