events { }
http {
    include /etc/nginx/mime.types;
    types {
        application/wasm wasm;
    }

    server {
        listen 80;
        
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html =404;
        }
        
        location /_framework/ {
            root /usr/share/nginx/html;
            add_header Cache-Control "no-cache";
        }
    }
}