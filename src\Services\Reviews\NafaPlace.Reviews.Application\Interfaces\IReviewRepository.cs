using NafaPlace.Reviews.Domain.Models;

namespace NafaPlace.Reviews.Application.Interfaces;

public interface IReviewRepository
{
    Task<Review?> GetByIdAsync(int id);
    Task<IEnumerable<Review>> GetByProductIdAsync(int productId, int page = 1, int pageSize = 10);
    Task<IEnumerable<Review>> GetByUserIdAsync(string userId, int page = 1, int pageSize = 10);
    Task<Review> CreateAsync(Review review);
    Task<Review> UpdateAsync(Review review);
    Task DeleteAsync(int id);
    Task<bool> ExistsAsync(int productId, string userId);
    Task<double> GetAverageRatingAsync(int productId);
    Task<int> GetTotalReviewsCountAsync(int productId);
    Task<Dictionary<int, int>> GetRatingDistributionAsync(int productId);
    Task<bool> MarkHelpfulAsync(int reviewId, string userId);
    Task<bool> UnmarkHelpfulAsync(int reviewId, string userId);
    Task<bool> HasUserMarkedHelpfulAsync(int reviewId, string userId);
}
