using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;

namespace NafaPlace.Reviews.Application.Services;

public class ReviewService : IReviewService
{
    private readonly IReviewRepository _reviewRepository;

    public ReviewService(IReviewRepository reviewRepository)
    {
        _reviewRepository = reviewRepository;
    }

    public async Task<ReviewDto?> GetReviewByIdAsync(int id)
    {
        var review = await _reviewRepository.GetByIdAsync(id);
        return review == null ? null : MapToDto(review);
    }

    public async Task<ReviewsPagedResult> GetReviewsByProductIdAsync(int productId, int page = 1, int pageSize = 10)
    {
        var reviews = await _reviewRepository.GetByProductIdAsync(productId, page, pageSize);
        var totalCount = await _reviewRepository.GetTotalReviewsCountAsync(productId);
        
        return new ReviewsPagedResult
        {
            Reviews = reviews.Select(MapToDto).ToList(),
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    public async Task<ReviewsPagedResult> GetReviewsByUserIdAsync(string userId, int page = 1, int pageSize = 10)
    {
        var reviews = await _reviewRepository.GetByUserIdAsync(userId, page, pageSize);
        
        return new ReviewsPagedResult
        {
            Reviews = reviews.Select(MapToDto).ToList(),
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<ReviewDto> CreateReviewAsync(CreateReviewRequest request)
    {
        // Check if user already reviewed this product
        var exists = await _reviewRepository.ExistsAsync(request.ProductId, request.UserId);
        if (exists)
        {
            throw new InvalidOperationException("User has already reviewed this product");
        }

        var review = new Review
        {
            ProductId = request.ProductId,
            UserId = request.UserId,
            UserName = request.UserName,
            Rating = request.Rating,
            Title = request.Title,
            Comment = request.Comment,
            IsVerifiedPurchase = request.IsVerifiedPurchase,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var createdReview = await _reviewRepository.CreateAsync(review);
        return MapToDto(createdReview);
    }

    public async Task<ReviewDto> UpdateReviewAsync(int id, UpdateReviewRequest request, string userId)
    {
        var review = await _reviewRepository.GetByIdAsync(id);
        if (review == null)
        {
            throw new ArgumentException("Review not found");
        }

        if (review.UserId != userId)
        {
            throw new UnauthorizedAccessException("User can only update their own reviews");
        }

        review.Rating = request.Rating;
        review.Title = request.Title;
        review.Comment = request.Comment;
        review.UpdatedAt = DateTime.UtcNow;

        var updatedReview = await _reviewRepository.UpdateAsync(review);
        return MapToDto(updatedReview);
    }

    public async Task DeleteReviewAsync(int id, string userId)
    {
        var review = await _reviewRepository.GetByIdAsync(id);
        if (review == null)
        {
            throw new ArgumentException("Review not found");
        }

        if (review.UserId != userId)
        {
            throw new UnauthorizedAccessException("User can only delete their own reviews");
        }

        await _reviewRepository.DeleteAsync(id);
    }

    public async Task<ReviewSummaryDto> GetReviewSummaryAsync(int productId)
    {
        var averageRating = await _reviewRepository.GetAverageRatingAsync(productId);
        var totalReviews = await _reviewRepository.GetTotalReviewsCountAsync(productId);
        var ratingDistribution = await _reviewRepository.GetRatingDistributionAsync(productId);

        return new ReviewSummaryDto
        {
            ProductId = productId,
            AverageRating = averageRating,
            TotalReviews = totalReviews,
            RatingDistribution = ratingDistribution
        };
    }

    public async Task<bool> MarkReviewHelpfulAsync(int reviewId, string userId)
    {
        return await _reviewRepository.MarkHelpfulAsync(reviewId, userId);
    }

    public async Task<bool> UnmarkReviewHelpfulAsync(int reviewId, string userId)
    {
        return await _reviewRepository.UnmarkHelpfulAsync(reviewId, userId);
    }

    public async Task<bool> CanUserReviewProductAsync(int productId, string userId)
    {
        return !await _reviewRepository.ExistsAsync(productId, userId);
    }

    private static ReviewDto MapToDto(Review review)
    {
        return new ReviewDto
        {
            Id = review.Id,
            ProductId = review.ProductId,
            UserId = review.UserId,
            UserName = review.UserName,
            Rating = review.Rating,
            Title = review.Title,
            Comment = review.Comment,
            CreatedAt = review.CreatedAt,
            UpdatedAt = review.UpdatedAt,
            IsVerifiedPurchase = review.IsVerifiedPurchase,
            HelpfulCount = review.HelpfulCount,
            IsApproved = review.IsApproved
        };
    }
}
