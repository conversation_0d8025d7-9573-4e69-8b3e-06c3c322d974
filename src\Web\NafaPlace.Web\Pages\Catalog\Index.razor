@page "/catalog"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Catalogue</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Filtres -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Filtres</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Catégories</h6>
                        @if (isLoading)
                        {
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                            </div>
                        }
                        else if (categories != null && categories.Any())
                        {
                            @foreach (var category in categories)
                            {
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" value="@category.Id"
                                           id="<EMAIL>" checked="@selectedCategories.Contains(category.Id)"
                                           @onchange="(e) => CategoryFilterChanged(category.Id, e.Value ?? false)">
                                    <label class="form-check-label" for="<EMAIL>">
                                        @category.Name
                                    </label>
                                </div>
                            }
                        }
                        else
                        {
                            <p>Aucune catégorie disponible</p>
                        }
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Prix</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control" placeholder="Min" @bind="minPrice">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" placeholder="Max" @bind="maxPrice">
                            </div>
                        </div>
                        <button class="btn btn-outline-primary w-100 mt-2" @onclick="ApplyPriceFilter">Appliquer</button>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Disponibilité</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" value="" id="inStock" @bind="inStockOnly">
                            <label class="form-check-label" for="inStock">
                                En stock uniquement
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-primary w-100" @onclick="ApplyFilters">
                        <i class="bi bi-funnel-fill me-1"></i> Filtrer
                    </button>
                    <button class="btn btn-outline-secondary w-100 mt-2" @onclick="ResetFilters">
                        <i class="bi bi-x-circle me-1"></i> Réinitialiser
                    </button>
                </div>
            </div>
        </div>

        <!-- Produits -->
        <div class="col-lg-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Catalogue de Produits</h2>
                <div class="d-flex align-items-center">
                    <label class="me-2">Trier par:</label>
                    <select class="form-select" @bind="sortOrder" @bind:after="SearchProducts">
                        <option value="CreatedAt">Plus récents</option>
                        <option value="price_asc">Prix croissant</option>
                        <option value="price_desc">Prix décroissant</option>
                        <option value="name_asc">Nom (A-Z)</option>
                        <option value="name_desc">Nom (Z-A)</option>
                    </select>
                </div>
            </div>

            @if (isLoading)
            {
                <div class="d-flex justify-content-center my-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (searchResponse?.Products != null && searchResponse.Products.Any())
            {
                <div class="row row-cols-1 row-cols-md-3 g-4">
                    @foreach (var product in searchResponse.Products)
                    {
                        <div class="col">
                            <div class="card h-100">
                                <div class="position-relative">
                                    @if (product.Images.Any())
                                    {
                                        <a href="/catalog/products/@product.Id">
                                            <img src="@ProductService.GetImageUrl(product.Images.First())" class="card-img-top" alt="@product.Name">
                                        </a>
                                    }
                                    else
                                    {
                                        <a href="/catalog/products/@product.Id">
                                            <img src="/images/no-image.png" class="card-img-top" alt="Image non disponible">
                                        </a>
                                    }
                                    
                                    @if (product.Stock <= 0)
                                    {
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-danger">Rupture de stock</span>
                                        </div>
                                    }
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h5>
                                    <p class="card-text text-muted small">@product.Category?.Name</p>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="text-primary fw-bold">@product.Price.ToString("N0") @product.Currency</span>
                                        <div class="ratings">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi @(i <= Math.Round(product.Rating) ? "bi-star-fill" : "bi-star")" style="color: #ffc107; font-size: 0.8rem;"></i>
                                            }
                                            <span class="text-muted ms-1" style="font-size: 0.8rem;">(@product.ReviewCount)</span>
                                        </div>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <a href="/catalog/products/@product.Id" class="btn btn-outline-primary btn-sm">Voir détails</a>
                                        <button class="btn btn-primary btn-sm" @onclick="() => AddToCart(product)" disabled="@(product.Stock <= 0)">
                                            <i class="bi bi-cart-plus"></i> Ajouter au panier
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (searchResponse.TotalPages > 1)
                {
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <a class="page-link" href="javascript:void(0);" @onclick="() => ChangePage(currentPage - 1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            
                            @for (int i = 1; i <= searchResponse.TotalPages; i++)
                            {
                                var pageNumber = i;
                                <li class="page-item @(currentPage == pageNumber ? "active" : "")">
                                    <a class="page-link" href="javascript:void(0);" @onclick="() => ChangePage(pageNumber)">@pageNumber</a>
                                </li>
                            }
                            
                            <li class="page-item @(currentPage == searchResponse.TotalPages ? "disabled" : "")">
                                <a class="page-link" href="javascript:void(0);" @onclick="() => ChangePage(currentPage + 1)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                }
            }
            else if (searchResponse == null || searchResponse.Products == null || !searchResponse.Products.Any())
            {
                <p class="text-center">Chargement des produits...</p>
            }
            else
            {
                <div class="alert alert-info" role="alert">
                    Aucun produit ne correspond à vos critères de recherche.
                </div>
            }
        </div>
    </div>
</div>

@code {
    private IEnumerable<CategoryDto> categories = Array.Empty<CategoryDto>();
    private ProductSearchResponse searchResponse = new ProductSearchResponse
    {
        Products = Array.Empty<ProductDto>(),
        TotalItems = 0,
        PageSize = 1
    };
    private List<int> selectedCategories = new List<int>();
    private decimal? minPrice;
    private decimal? maxPrice;
    private bool inStockOnly;
    private string sortOrder = "CreatedAt";
    private int currentPage = 1;
    private int pageSize = 9;
    private bool isLoading = true;

    private string _userId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
            Console.WriteLine($"🔍 DEBUG Index: UserId récupéré = '{_userId}'");

            // Debug de toutes les claims
            Console.WriteLine("🔍 DEBUG Index: Toutes les claims:");
            foreach (var claim in user.Claims)
            {
                Console.WriteLine($"  - {claim.Type}: {claim.Value}");
            }
        }
        else
        {
            Console.WriteLine("❌ DEBUG Index: Utilisateur non authentifié");
        }

        // Récupérer le paramètre categoryId depuis l'URL
        var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
        var query = uri.Query;
        if (!string.IsNullOrEmpty(query))
        {
            var categoryIdMatch = System.Text.RegularExpressions.Regex.Match(query, @"categoryId=(\d+)");
            if (categoryIdMatch.Success && int.TryParse(categoryIdMatch.Groups[1].Value, out var categoryId))
            {
                selectedCategories.Add(categoryId);
            }
        }

        await LoadCategories();
        await SearchProducts();
    }

    private async Task LoadCategories()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des catégories: {ex.Message}");
            // Gérer l'erreur
        }
    }

    private async Task SearchProducts()
    {
        try
        {
            isLoading = true;
            
            var request = new ProductSearchRequest
            {
                CategoryIds = selectedCategories.Any() ? selectedCategories : null,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                InStockOnly = inStockOnly,
                Page = currentPage,
                PageSize = pageSize
            };

            switch (sortOrder)
            {
                case "price_asc":
                    request.SortBy = "price";
                    request.SortDescending = false;
                    break;
                case "price_desc":
                    request.SortBy = "price";
                    request.SortDescending = true;
                    break;
                case "name_asc":
                    request.SortBy = "name";
                    request.SortDescending = false;
                    break;
                case "name_desc":
                    request.SortBy = "name";
                    request.SortDescending = true;
                    break;
                case "CreatedAt":
                default:
                    request.SortBy = "CreatedAt";
                    request.SortDescending = true;
                    break;
            }
            
            searchResponse = await ProductService.SearchProductsAsync(request);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la recherche de produits: {ex.Message}");
            // Gérer l'erreur
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task CategoryFilterChanged(int categoryId, object isChecked)
    {
        if (isChecked != null && (bool)isChecked)
        {
            if (!selectedCategories.Contains(categoryId))
            {
                selectedCategories.Add(categoryId);
            }
        }
        else
        {
            selectedCategories.Remove(categoryId);
        }
        
        // Déclencher la recherche immédiatement après le changement de catégorie
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ApplyPriceFilter()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task OnInStockFilterChanged()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ApplyFilters()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ResetFilters()
    {
        selectedCategories.Clear();
        minPrice = null;
        maxPrice = null;
        inStockOnly = false;
        sortOrder = "CreatedAt";
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ChangePage(int page)
    {
        if (page < 1 || (searchResponse != null && page > searchResponse.TotalPages))
            return;
            
        currentPage = page;
        await SearchProducts();
    }

    private async Task AddToCart(ProductDto product)
    {
        Console.WriteLine($"🔍 DEBUG: Tentative d'ajout au panier - UserId: {_userId}, ProductId: {product.Id}");

        if (string.IsNullOrEmpty(_userId))
        {
            Console.WriteLine("❌ DEBUG: Utilisateur non connecté, redirection vers login");
            NavigationManager.NavigateTo("login");
            return;
        }

        if (product.Stock > 0)
        {
            try
            {
                Console.WriteLine($"🛒 DEBUG: Création de l'item panier - ProductId: {product.Id}, Quantity: 1");
                var cartItem = new CartItemCreateDto { ProductId = product.Id, Quantity = 1 };

                Console.WriteLine($"📡 DEBUG: Appel API AddItemToCartAsync...");
                var result = await CartService.AddItemToCartAsync(_userId, cartItem);

                Console.WriteLine($"✅ DEBUG: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

                // Notification de succès
                await JSRuntime.InvokeVoidAsync("showToast", $"✅ {product.Name} ajouté au panier !", "success");
                Console.WriteLine($"Produit {product.Name} ajouté au panier.");
            }
            catch (Exception ex)
            {
                // Notification d'erreur
                Console.WriteLine($"❌ DEBUG: Erreur complète: {ex}");
                await JSRuntime.InvokeVoidAsync("showToast", "❌ Erreur lors de l'ajout au panier", "danger");
                Console.WriteLine($"Erreur lors de l'ajout au panier: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine($"⚠️ DEBUG: Produit en rupture de stock - Stock: {product.Stock}");
            await JSRuntime.InvokeVoidAsync("showToast", "⚠️ Produit en rupture de stock", "warning");
        }
    }
}
