using NafaPlace.Reviews.Application.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public interface IReviewService
{
    Task<ReviewDto?> GetReviewByIdAsync(int id);
    Task<ReviewsPagedResult> GetReviewsByProductIdAsync(int productId, int page = 1, int pageSize = 10);
    Task<ReviewsPagedResult> GetReviewsByUserIdAsync(string userId, int page = 1, int pageSize = 10);
    Task<ReviewDto> CreateReviewAsync(CreateReviewRequest request);
    Task<ReviewDto> UpdateReviewAsync(int id, UpdateReviewRequest request, string userId);
    Task DeleteReviewAsync(int id, string userId);
    Task<ReviewSummaryDto> GetReviewSummaryAsync(int productId);
    Task<bool> MarkReviewHelpfulAsync(int reviewId, string userId);
    Task<bool> UnmarkReviewHelpfulAsync(int reviewId, string userId);
    Task<bool> CanUserReviewProductAsync(int productId, string userId);
}
