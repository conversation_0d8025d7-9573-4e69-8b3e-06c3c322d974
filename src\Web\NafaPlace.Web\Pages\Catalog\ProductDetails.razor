@page "/catalog/products/{id:int}"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Web.Services
@using System.Security.Claims
@inject IProductService ProductService
@inject NavigationManager NavigationManager

@if (_product == null)
{
    <p><em>Chargement du produit...</em></p>
}
else
{
    <div class="container my-5">
        <div class="row">
            <div class="col-md-6">
                <img src="@(_product.Images.Any() ? _product.Images.First().Url : "/images/placeholder.png")" class="img-fluid" alt="@_product.Name">
            </div>
            <div class="col-md-6">
                <h2>@_product.Name</h2>
                <p class="text-muted">@_product.Category?.Name</p>
                <h3 class="mb-3">@((_selectedVariant?.Price ?? _product.Price).ToString("C"))</h3>
                <p>@_product.Description</p>

                <hr>

                <!-- Variantes -->
                @if (_product.Variants != null && _product.Variants.Any())
                {
                    <h5>Variantes</h5>
                    <div class="mb-3">
                        @foreach (var variant in _product.Variants)
                        {
                            <button class="btn @(_selectedVariant?.Id == variant.Id ? "btn-primary" : "btn-outline-primary") me-2" @onclick="() => SelectVariant(variant)">
                                @variant.Name
                            </button>
                        }
                    </div>
                }

                <div class="d-flex mt-4">
                    <input type="number" class="form-control me-3" style="width: 80px;" @bind="_quantity" min="1">
                    <button class="btn btn-primary flex-grow-1" @onclick="AddToCart">
                        <i class="fas fa-shopping-cart me-2"></i> Ajouter au panier
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public int Id { get; set; }

    private ProductDto? _product;
    private ProductVariantDto? _selectedVariant;
    private int _quantity = 1;

    [Inject]
    private ICartService CartService { get; set; } = null!;

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        _product = await ProductService.GetProductByIdAsync(Id);
        if (_product?.Variants?.Any() == true)
        {
            _selectedVariant = _product.Variants.First();
        }
    }

    private void SelectVariant(ProductVariantDto variant)
    {
        _selectedVariant = variant;
    }

    private async Task AddToCart()
    {
        var item = new CartItemCreateDto
        {
            ProductId = _product.Id,
            ProductName = _product.Name,
            Price = _selectedVariant?.Price ?? _product.Price,
            Quantity = _quantity,
            VariantId = _selectedVariant?.Id,
            VariantName = _selectedVariant?.Name
        };

        var authState = await AuthenticationStateTask;
        var user = authState.User;
        if (!user.Identity.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/login");
            return;
        }
        var userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;

        await CartService.AddItemToCartAsync(userId, item);
        NavigationManager.NavigateTo("/cart");
    }
}
