using NafaPlace.Cart.Application;
using NafaPlace.Cart.Application.Services;
using NafaPlace.Cart.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = builder.Configuration.GetConnectionString("Redis");
    options.InstanceName = "NafaPlace.Cart.API_";
});

builder.Services.AddScoped<IShoppingCartRepository, ShoppingCartRepository>();
builder.Services.AddScoped<ICartService, CartService>();

// Configuration du HttpClient pour l'API Catalog
builder.Services.AddHttpClient<IProductService, ProductService>(client =>
{
    var catalogApiUrl = builder.Configuration.GetValue<string>("CatalogApiUrl") ?? "http://localhost:5243";
    Console.WriteLine($"🔧 Configuration Cart API - CatalogApiUrl: {catalogApiUrl}");
    client.BaseAddress = new Uri(catalogApiUrl);
});

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowPortals", policy =>
    {
        policy.WithOrigins(
                "http://localhost:8080",   // Client Portal
                "http://localhost:8081",   // Admin Portal
                "http://localhost:8082"    // Seller Portal
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials();
    });
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowPortals");

app.UseAuthorization();

app.MapControllers();

app.Run();
