// Fonctions pour gérer les modals Bootstrap
window.showModal = function (modalId) {
    var modalElement = document.getElementById(modalId);
    if (modalElement) {
        var modal = new bootstrap.Modal(modalElement);
        modal.show();
    }
};

window.hideModal = function (modalId) {
    var modalElement = document.getElementById(modalId);
    if (modalElement) {
        var modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }
    }
};

// Fonction pour afficher des notifications toast
window.showToast = function (type, title, message) {
    // Vérifier si Toastr est disponible
    if (typeof toastr !== 'undefined') {
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-right",
            timeOut: 5000
        };

        switch (type) {
            case 'success':
                toastr.success(message, title);
                break;
            case 'error':
                toastr.error(message, title);
                break;
            case 'warning':
                toastr.warning(message, title);
                break;
            case 'info':
                toastr.info(message, title);
                break;
            default:
                toastr.info(message, title);
                break;
        }
    } else {
        // Fallback si Toastr n'est pas disponible
        alert(title + ": " + message);
    }
};

// Fonction pour initialiser les dropdowns Bootstrap
window.initializeDropdowns = function () {
    // Sélectionner tous les éléments avec data-bs-toggle="dropdown"
    var dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    
    // Créer une instance Dropdown pour chaque élément
    dropdownElementList.forEach(function (dropdownToggleEl) {
        try {
            // Vérifier si l'élément a déjà une instance Dropdown
            var existingDropdown = bootstrap.Dropdown.getInstance(dropdownToggleEl);
            if (!existingDropdown) {
                // Créer une nouvelle instance Dropdown si elle n'existe pas déjà
                new bootstrap.Dropdown(dropdownToggleEl);
            }
        } catch (e) {
            console.error("Erreur lors de l'initialisation du dropdown:", e);
        }
    });
    
    // Ajouter des gestionnaires d'événements pour les clics sur les éléments dropdown
    document.addEventListener('click', function (event) {
        var target = event.target;
        // Vérifier si l'élément cliqué ou un de ses parents a l'attribut data-bs-toggle="dropdown"
        while (target && target !== document) {
            if (target.getAttribute('data-bs-toggle') === 'dropdown') {
                try {
                    var dropdown = bootstrap.Dropdown.getInstance(target);
                    if (dropdown) {
                        dropdown.toggle();
                    } else {
                        dropdown = new bootstrap.Dropdown(target);
                        dropdown.toggle();
                    }
                    event.preventDefault();
                    return;
                } catch (e) {
                    console.error("Erreur lors du toggle du dropdown:", e);
                }
            }
            target = target.parentNode;
        }
    });
};

// Initialiser les dropdowns au chargement de la page
document.addEventListener('DOMContentLoaded', function () {
    console.log("DOMContentLoaded - Initialisation des dropdowns");
    window.initializeDropdowns();
});

// Fonction pour réinitialiser les dropdowns (à appeler après des mises à jour dynamiques)
window.reinitializeDropdowns = function () {
    console.log("Réinitialisation des dropdowns");
    window.initializeDropdowns();
};
