using NafaPlace.Payment.API.Models;
using NafaPlace.Payment.API.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.Configure<StripeSettings>(builder.Configuration.GetSection("StripeSettings"));
builder.Services.Configure<OrangeMoneySettings>(builder.Configuration.GetSection("OrangeMoneySettings"));

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowPortals", policy =>
    {
        policy.WithOrigins(
                "http://localhost:8080",   // Client Portal
                "http://localhost:8081",   // Admin Portal
                "http://localhost:8082"    // Seller Portal
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials();
    });
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add HTTP Client for Orange Money
builder.Services.AddHttpClient<IOrangeMoneyService, OrangeMoneyService>();
builder.Services.AddScoped<IOrangeMoneyService, OrangeMoneyService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowPortals");

app.UseAuthorization();

app.MapControllers();

app.Run();
