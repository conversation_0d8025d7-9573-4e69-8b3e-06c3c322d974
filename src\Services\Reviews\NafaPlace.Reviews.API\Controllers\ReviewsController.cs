using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Reviews.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ReviewsController : ControllerBase
{
    private readonly IReviewService _reviewService;

    public ReviewsController(IReviewService reviewService)
    {
        _reviewService = reviewService;
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ReviewDto>> GetReview(int id)
    {
        var review = await _reviewService.GetReviewByIdAsync(id);
        if (review == null)
            return NotFound();

        return Ok(review);
    }

    [HttpGet("product/{productId}")]
    public async Task<ActionResult<ReviewsPagedResult>> GetReviewsByProduct(
        int productId, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10)
    {
        var result = await _reviewService.GetReviewsByProductIdAsync(productId, page, pageSize);
        return Ok(result);
    }

    [HttpGet("user/{userId}")]
    [Authorize]
    public async Task<ActionResult<ReviewsPagedResult>> GetReviewsByUser(
        string userId, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10)
    {
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (currentUserId != userId)
            return Forbid();

        var result = await _reviewService.GetReviewsByUserIdAsync(userId, page, pageSize);
        return Ok(result);
    }

    [HttpGet("product/{productId}/summary")]
    public async Task<ActionResult<ReviewSummaryDto>> GetReviewSummary(int productId)
    {
        var summary = await _reviewService.GetReviewSummaryAsync(productId);
        return Ok(summary);
    }

    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ReviewDto>> CreateReview([FromBody] CreateReviewRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var currentUserName = User.FindFirst(ClaimTypes.Name)?.Value;

            if (string.IsNullOrEmpty(currentUserId) || string.IsNullOrEmpty(currentUserName))
                return Unauthorized();

            request.UserId = currentUserId;
            request.UserName = currentUserName;

            var review = await _reviewService.CreateReviewAsync(request);
            return CreatedAtAction(nameof(GetReview), new { id = review.Id }, review);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<ReviewDto>> UpdateReview(int id, [FromBody] UpdateReviewRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            var review = await _reviewService.UpdateReviewAsync(id, request, currentUserId);
            return Ok(review);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteReview(int id)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            await _reviewService.DeleteReviewAsync(id, currentUserId);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPost("{id}/helpful")]
    [Authorize]
    public async Task<IActionResult> MarkHelpful(int id)
    {
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(currentUserId))
            return Unauthorized();

        var result = await _reviewService.MarkReviewHelpfulAsync(id, currentUserId);
        if (!result)
            return BadRequest("Review already marked as helpful by this user");

        return Ok();
    }

    [HttpDelete("{id}/helpful")]
    [Authorize]
    public async Task<IActionResult> UnmarkHelpful(int id)
    {
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(currentUserId))
            return Unauthorized();

        var result = await _reviewService.UnmarkReviewHelpfulAsync(id, currentUserId);
        if (!result)
            return BadRequest("Review not marked as helpful by this user");

        return Ok();
    }

    [HttpGet("product/{productId}/can-review")]
    [Authorize]
    public async Task<ActionResult<bool>> CanUserReviewProduct(int productId)
    {
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(currentUserId))
            return Unauthorized();

        var canReview = await _reviewService.CanUserReviewProductAsync(productId, currentUserId);
        return Ok(canReview);
    }
}
