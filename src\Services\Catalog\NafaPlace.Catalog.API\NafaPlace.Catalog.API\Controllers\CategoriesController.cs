using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Category;
using System;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/categories")]
    public class CategoriesController : ControllerBase
    {
        private readonly ICategoryService _categoryService;

        public CategoriesController(ICategoryService categoryService)
        {
            _categoryService = categoryService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllCategories()
        {
            var categories = await _categoryService.GetAllCategoriesAsync();
            return Ok(categories);
        }

        [HttpGet("main")]
        public async Task<IActionResult> GetMainCategories()
        {
            var categories = await _categoryService.GetMainCategoriesAsync();
            return Ok(categories);
        }

        /// <summary>
        /// Endpoint pour récupérer les vendeurs
        /// </summary>
        [HttpGet("seller-list")]
        public async Task<IActionResult> GetVendors()
        {
            try
            {
                // Retourner des vendeurs de test pour l'instant
                var sellers = new[]
                {
                    new { Id = 1, Name = "Électronique Mali", Email = "<EMAIL>", PhoneNumber = "+22379123456", Address = "123 Rue Bamako, Mali", IsActive = true, IsVerified = true },
                    new { Id = 2, Name = "Mode Africaine", Email = "<EMAIL>", PhoneNumber = "+22378456789", Address = "45 Avenue Dakar, Sénégal", IsActive = true, IsVerified = true },
                    new { Id = 3, Name = "Tech Guinée", Email = "<EMAIL>", PhoneNumber = "+22470123456", Address = "789 Boulevard Conakry, Guinée", IsActive = true, IsVerified = true }
                };

                return Ok(sellers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des vendeurs", error = ex.Message });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetCategoryById(int id)
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
                return NotFound();

            return Ok(category);
        }

        [HttpGet("{id:int}/subcategories")]
        public async Task<IActionResult> GetSubcategories(int id)
        {
            var subcategories = await _categoryService.GetSubcategoriesAsync(id);
            return Ok(subcategories);
        }

        [HttpPost]
        public async Task<IActionResult> CreateCategory(CreateCategoryRequest request)
        {
            var category = await _categoryService.CreateCategoryAsync(request);
            return CreatedAtAction(nameof(GetCategoryById), new { id = category.Id }, category);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCategory(int id, UpdateCategoryRequest request)
        {
            var category = await _categoryService.UpdateCategoryAsync(id, request);
            if (category == null)
                return NotFound();

            return Ok(category);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            try
            {
                var result = await _categoryService.DeleteCategoryAsync(id);
                if (!result)
                    return BadRequest("Impossible de supprimer cette catégorie car elle contient des produits ou des sous-catégories");

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Une erreur est survenue lors de la suppression de la catégorie: {ex.Message}");
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchCategories([FromQuery] string searchTerm)
        {
            var categories = await _categoryService.SearchCategoriesAsync(searchTerm);
            return Ok(categories);
        }

        /// <summary>
        /// Upload une image pour une catégorie
        /// </summary>
        /// <param name="request">Données de l'image en base64</param>
        /// <returns>URL de l'image uploadée</returns>
        [HttpPost("upload-image")]
        [ProducesResponseType(typeof(CategoryImageUploadResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<CategoryImageUploadResponse>> UploadCategoryImage([FromBody] CategoryImageUploadRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Image))
                {
                    return BadRequest(new { error = "Image data is required" });
                }

                var imageUrl = await _categoryService.UploadCategoryImageAsync(request.Image);

                if (string.IsNullOrEmpty(imageUrl))
                {
                    return BadRequest(new { error = "Failed to upload image" });
                }

                return Ok(new CategoryImageUploadResponse { ImageUrl = imageUrl });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }

    public class CategoryImageUploadRequest
    {
        public string Image { get; set; } = string.Empty;
    }

    public class CategoryImageUploadResponse
    {
        public string ImageUrl { get; set; } = string.Empty;
    }
}
