using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Application.DTOs.Auth;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure.Utils;
using System.Security.Claims;

namespace NafaPlace.Identity.Infrastructure.Services;

public class AuthService : IAuthService
{
    private readonly IdentityDbContext _context;
    private readonly IJwtService _jwtService;
    private readonly ILogger<AuthService> _logger;
    private readonly Microsoft.AspNetCore.Identity.IPasswordHasher<User> _passwordHasher;

    public AuthService(IdentityDbContext context, IJwtService jwtService, ILogger<AuthService> logger, Microsoft.AspNetCore.Identity.IPasswordHasher<User> passwordHasher)
    {
        _context = context;
        _jwtService = jwtService;
        _logger = logger;
        _passwordHasher = passwordHasher;
    }

    public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
    {
        // Vérifier si l'email existe déjà
        var existingUserByEmail = await _context.Users.FirstOrDefaultAsync(u => u.Email == request.Email);
        if (existingUserByEmail != null)
        {
            throw new ValidationException("Un utilisateur avec cet email existe déjà.");
        }

        // Vérifier si le nom d'utilisateur existe déjà
        var existingUserByUsername = await _context.Users.FirstOrDefaultAsync(u => u.Username == request.Username);
        if (existingUserByUsername != null)
        {
            throw new ValidationException("Ce nom d'utilisateur est déjà pris.");
        }

        var user = new User
        {
            Email = request.Email,
            Username = request.Username,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Roles = "User", // Rôle par défaut
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        user.PasswordHash = _passwordHasher.HashPassword(user, request.Password);

        _context.Users.Add(user);

        // Générer un refresh token
        var refreshToken = _jwtService.GenerateRefreshToken();
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

        await _context.SaveChangesAsync();

        // Assigner le rôle User par défaut
        var userRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "User");
        if (userRole != null)
        {
            var userRoleAssignment = new UserRole
            {
                UserId = user.Id,
                RoleId = userRole.Id
            };
            _context.UserRoles.Add(userRoleAssignment);
            await _context.SaveChangesAsync();
        }

        // Générer un access token
        var roles = new[] { "User" };
        var accessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles);

        return new AuthResponse
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            User = MapToUserDto(user)
        };
    }

    public async Task<AuthResponse> RegisterAsync(RegisterRequest request, string role)
    {
        // Vérifier si l'email existe déjà
        var existingUserByEmail = await _context.Users.FirstOrDefaultAsync(u => u.Email == request.Email);
        if (existingUserByEmail != null)
        {
            throw new ValidationException("Un utilisateur avec cet email existe déjà.");
        }

        // Vérifier si le nom d'utilisateur existe déjà
        var existingUserByUsername = await _context.Users.FirstOrDefaultAsync(u => u.Username == request.Username);
        if (existingUserByUsername != null)
        {
            throw new ValidationException("Ce nom d'utilisateur est déjà pris.");
        }

        var user = new User
        {
            Email = request.Email,
            Username = request.Username,
            FirstName = request.FirstName,
            LastName = request.LastName,
            Roles = role, // Utilisation du rôle spécifié
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        user.PasswordHash = _passwordHasher.HashPassword(user, request.Password);

        _context.Users.Add(user);

        // Générer un refresh token
        var refreshToken = _jwtService.GenerateRefreshToken();
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

        await _context.SaveChangesAsync();

        // Assigner le rôle spécifié
        var userRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == role);
        if (userRole != null)
        {
            var userRoleAssignment = new UserRole
            {
                UserId = user.Id,
                RoleId = userRole.Id
            };
            _context.UserRoles.Add(userRoleAssignment);
            await _context.SaveChangesAsync();
        }

        // Générer un access token
        var roles = new[] { role };
        var accessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles);

        return new AuthResponse
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            User = MapToUserDto(user)
        };
    }

    public async Task<AuthResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            _logger.LogInformation("Tentative de connexion pour l'utilisateur: {Username}", request.Username);
            
            // Vérifier si l'utilisateur existe par nom d'utilisateur ou email et charger ses rôles
            var user = await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u =>
                    u.Username == request.Username ||
                    u.Email == request.Username); // Permet de se connecter avec l'email dans le champ Username
            
            if (user == null)
            {
                _logger.LogWarning("Échec de connexion: utilisateur non trouvé pour {Username}", request.Username);
                throw new AuthenticationException("Nom d'utilisateur ou mot de passe incorrect.");
            }

            _logger.LogInformation("Utilisateur trouvé: {UserId}, vérification du mot de passe", user.Id);
            
            if (_passwordHasher.VerifyHashedPassword(user, user.PasswordHash, request.Password) == Microsoft.AspNetCore.Identity.PasswordVerificationResult.Failed)
            {
                _logger.LogWarning("Échec de connexion: mot de passe incorrect pour {Username}", request.Username);
                throw new AuthenticationException("Nom d'utilisateur ou mot de passe incorrect.");
            }

            if (!user.IsActive)
            {
                _logger.LogWarning("Échec de connexion: compte désactivé pour {Username}", request.Username);
                throw new AuthenticationException("Ce compte est désactivé.");
            }

            _logger.LogInformation("Authentification réussie pour {Username}, génération des tokens", request.Username);
            
            // Générer un refresh token
            var refreshToken = _jwtService.GenerateRefreshToken();
            user.RefreshToken = refreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

            // Mettre à jour la date de dernière connexion
            user.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Générer un access token avec les rôles de l'utilisateur
            var roles = user.UserRoles.Select(ur => ur.Role.Name).ToArray();
            var accessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles);

            _logger.LogInformation("Connexion réussie pour {Username}, tokens générés", request.Username);
            
            return new AuthResponse
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                User = MapToUserDto(user),
                Token = accessToken // Assurer la compatibilité avec le client
            };
        }
        catch (Exception ex) when (ex is not AuthenticationException)
        {
            _logger.LogError(ex, "Erreur inattendue lors de la connexion pour {Username}", request.Username);
            throw new AuthenticationException($"Une erreur s'est produite lors de la connexion: {ex.Message}");
        }
    }

    public async Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request)
    {
        var principal = _jwtService.GetPrincipalFromExpiredToken(request.AccessToken);
        var userId = int.Parse(principal.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? throw new AuthenticationException("Token invalide"));

        var user = await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null || user.RefreshToken != request.RefreshToken || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        {
            throw new AuthenticationException("Token de rafraîchissement invalide ou expiré");
        }

        // Générer un nouveau refresh token
        var newRefreshToken = _jwtService.GenerateRefreshToken();
        user.RefreshToken = newRefreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

        await _context.SaveChangesAsync();

        // Générer un nouveau access token avec les rôles de l'utilisateur
        var roles = user.UserRoles.Select(ur => ur.Role.Name).ToArray();
        var newAccessToken = _jwtService.GenerateAccessToken(user.Id, user.Username, roles);

        return new AuthResponse
        {
            AccessToken = newAccessToken,
            RefreshToken = newRefreshToken,
            User = MapToUserDto(user)
        };
    }

    public async Task LogoutAsync(int userId)
    {
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user != null)
        {
            // Révoquer le refresh token
            user.RefreshToken = null;
            user.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<UserDto> GetUserByIdAsync(int userId)
    {
        var user = await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null)
        {
            throw new NotFoundException($"Utilisateur avec l'ID {userId} non trouvé");
        }

        return MapToUserDto(user);
    }

    private UserDto MapToUserDto(User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Roles = user.UserRoles?.Select(ur => ur.Role.Name).ToList() ?? new List<string>(),
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };
    }
}
