using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.AdminPortal.Models.Reviews;
using Blazored.LocalStorage;
using System.Net.Http.Headers;

namespace NafaPlace.AdminPortal.Services;

public class ReviewService : IReviewService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly JsonSerializerOptions _jsonOptions;

    public ReviewService(HttpClient httpClient, ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<AdminReviewsPagedResponse> GetAdminReviewsAsync(AdminReviewFilterRequest request)
    {
        try
        {
            await SetAuthorizationHeader();
            
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={request.Status}");
            if (request.Rating.HasValue)
                queryParams.Add($"rating={request.Rating}");
            if (request.IsVerifiedPurchase.HasValue)
                queryParams.Add($"isVerifiedPurchase={request.IsVerifiedPurchase}");
            if (!string.IsNullOrEmpty(request.DateFilter))
                queryParams.Add($"dateFilter={request.DateFilter}");
            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            if (!string.IsNullOrEmpty(request.SellerId))
                queryParams.Add($"sellerId={request.SellerId}");
            if (request.ProductId.HasValue)
                queryParams.Add($"productId={request.ProductId}");
            if (request.HasReports.HasValue)
                queryParams.Add($"hasReports={request.HasReports}");
            queryParams.Add($"page={request.Page}");
            queryParams.Add($"pageSize={request.PageSize}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/reviews/admin?{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<AdminReviewsPagedResponse>(content, _jsonOptions) ?? new AdminReviewsPagedResponse();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des avis admin: {ex.Message}");
        }

        // Fallback to demo data
        return GetDemoAdminReviewsData(request);
    }

    public async Task<AdminReviewStatsDto> GetAdminReviewStatsAsync()
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync("api/reviews/admin/stats");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<AdminReviewStatsDto>(content, _jsonOptions) ?? new AdminReviewStatsDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques admin: {ex.Message}");
        }

        // Fallback to demo data
        return new AdminReviewStatsDto
        {
            TotalReviews = 156,
            PendingReviews = 12,
            ApprovedReviews = 132,
            RejectedReviews = 12,
            ReportedReviews = 3,
            AverageRating = 4.3,
            RatingDistribution = new Dictionary<int, int>
            {
                { 5, 68 },
                { 4, 45 },
                { 3, 28 },
                { 2, 10 },
                { 1, 5 }
            },
            ReviewsByPeriod = new Dictionary<string, int>
            {
                { "Cette semaine", 15 },
                { "Ce mois", 42 },
                { "Ce trimestre", 89 }
            }
        };
    }

    public async Task<AdminReviewDto?> GetReviewByIdAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"api/reviews/admin/{reviewId}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<AdminReviewDto>(content, _jsonOptions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de l'avis: {ex.Message}");
        }

        return null;
    }

    public async Task<bool> ApproveReviewAsync(int reviewId, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new ReviewModerationRequest
            {
                ReviewId = reviewId,
                IsApproved = true,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("api/reviews/admin/moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'approbation de l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> RejectReviewAsync(int reviewId, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new ReviewModerationRequest
            {
                ReviewId = reviewId,
                IsApproved = false,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("api/reviews/admin/moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du rejet de l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> DeleteReviewAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.DeleteAsync($"api/reviews/admin/{reviewId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> BulkApproveReviewsAsync(List<int> reviewIds, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new BulkReviewModerationRequest
            {
                ReviewIds = reviewIds,
                IsApproved = true,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("api/reviews/admin/bulk-moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'approbation en lot: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> BulkRejectReviewsAsync(List<int> reviewIds, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new BulkReviewModerationRequest
            {
                ReviewIds = reviewIds,
                IsApproved = false,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("api/reviews/admin/bulk-moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du rejet en lot: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> BulkDeleteReviewsAsync(List<int> reviewIds)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.PostAsJsonAsync("api/reviews/admin/bulk-delete", reviewIds);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression en lot: {ex.Message}");
            return false;
        }
    }

    public async Task<List<ReviewReportDto>> GetReviewReportsAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"api/reviews/admin/{reviewId}/reports");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ReviewReportDto>>(content, _jsonOptions) ?? new List<ReviewReportDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des signalements: {ex.Message}");
        }

        return new List<ReviewReportDto>();
    }

    public async Task<bool> ResolveReviewReportAsync(int reportId, string resolution)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new { Resolution = resolution };
            var response = await _httpClient.PostAsJsonAsync($"api/reviews/admin/reports/{reportId}/resolve", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la résolution du signalement: {ex.Message}");
            return false;
        }
    }

    public async Task<ReviewAnalyticsDto> GetReviewAnalyticsAsync(string period = "month")
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"api/reviews/admin/analytics?period={period}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewAnalyticsDto>(content, _jsonOptions) ?? new ReviewAnalyticsDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des analytics: {ex.Message}");
        }

        return new ReviewAnalyticsDto();
    }

    private async Task SetAuthorizationHeader()
    {
        var token = await _localStorage.GetItemAsync<string>("authToken");
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }
    }

    private AdminReviewsPagedResponse GetDemoAdminReviewsData(AdminReviewFilterRequest request)
    {
        var allReviews = new List<AdminReviewDto>
        {
            new() { Id = 1, ProductId = 1, ProductName = "Smartphone Samsung Galaxy", ProductImageUrl = "/images/products/samsung.jpg", 
                   SellerId = "seller1", SellerName = "TechStore Guinea", UserId = "user1", UserName = "Mamadou Diallo", UserEmail = "<EMAIL>",
                   Rating = 5, Title = "Excellent produit", Comment = "Très satisfait de cet achat, le téléphone fonctionne parfaitement.", 
                   IsApproved = null, IsVerifiedPurchase = true, HelpfulCount = 3, ReportCount = 0, CreatedAt = DateTime.Now.AddDays(-1) },
            
            new() { Id = 2, ProductId = 1, ProductName = "Smartphone Samsung Galaxy", ProductImageUrl = "/images/products/samsung.jpg", 
                   SellerId = "seller1", SellerName = "TechStore Guinea", UserId = "user2", UserName = "Fatoumata Camara", UserEmail = "<EMAIL>",
                   Rating = 4, Title = "Bon rapport qualité-prix", Comment = "Le produit correspond à mes attentes, livraison rapide.", 
                   IsApproved = true, IsVerifiedPurchase = true, HelpfulCount = 1, ReportCount = 0, CreatedAt = DateTime.Now.AddDays(-3),
                   ApprovedAt = DateTime.Now.AddDays(-2), ApprovedBy = "admin1" },
            
            new() { Id = 3, ProductId = 2, ProductName = "Ordinateur Portable HP", ProductImageUrl = "/images/products/hp-laptop.jpg", 
                   SellerId = "seller2", SellerName = "ElectroMax", UserId = "user3", UserName = "Ibrahima Sow", UserEmail = "<EMAIL>",
                   Rating = 2, Title = "Décevant", Comment = "L'ordinateur a des problèmes de performance, pas satisfait.", 
                   IsApproved = null, IsVerifiedPurchase = true, HelpfulCount = 0, ReportCount = 1, CreatedAt = DateTime.Now.AddHours(-6) },
            
            new() { Id = 4, ProductId = 3, ProductName = "Casque Audio Sony", ProductImageUrl = "/images/products/sony-headphones.jpg", 
                   SellerId = "seller3", SellerName = "AudioPro", UserId = "user4", UserName = "Aminata Touré", UserEmail = "<EMAIL>",
                   Rating = 5, Title = "Son exceptionnel", Comment = "La qualité audio est remarquable, très confortable à porter.", 
                   IsApproved = true, IsVerifiedPurchase = false, HelpfulCount = 5, ReportCount = 0, CreatedAt = DateTime.Now.AddDays(-5),
                   ApprovedAt = DateTime.Now.AddDays(-4), ApprovedBy = "admin1" },
            
            new() { Id = 5, ProductId = 4, ProductName = "Tablette iPad Air", ProductImageUrl = "/images/products/ipad.jpg", 
                   SellerId = "seller1", SellerName = "TechStore Guinea", UserId = "user5", UserName = "Ousmane Barry", UserEmail = "<EMAIL>",
                   Rating = 1, Title = "Produit défectueux", Comment = "La tablette ne s'allume plus après 2 jours d'utilisation.", 
                   IsApproved = false, IsVerifiedPurchase = true, HelpfulCount = 0, ReportCount = 0, CreatedAt = DateTime.Now.AddDays(-7),
                   ApprovedAt = DateTime.Now.AddDays(-6), ApprovedBy = "admin2", RejectionReason = "Avis non constructif" }
        };

        // Apply filters
        var filteredReviews = allReviews.AsQueryable();

        if (!string.IsNullOrEmpty(request.Status))
        {
            filteredReviews = request.Status switch
            {
                "approved" => filteredReviews.Where(r => r.IsApproved == true),
                "pending" => filteredReviews.Where(r => r.IsApproved == null),
                "rejected" => filteredReviews.Where(r => r.IsApproved == false),
                _ => filteredReviews
            };
        }

        if (request.Rating.HasValue && request.Rating > 0)
            filteredReviews = filteredReviews.Where(r => r.Rating == request.Rating);

        if (request.IsVerifiedPurchase.HasValue)
            filteredReviews = filteredReviews.Where(r => r.IsVerifiedPurchase == request.IsVerifiedPurchase);

        if (!string.IsNullOrEmpty(request.DateFilter))
        {
            var now = DateTime.Now;
            filteredReviews = request.DateFilter switch
            {
                "today" => filteredReviews.Where(r => r.CreatedAt.Date == now.Date),
                "week" => filteredReviews.Where(r => r.CreatedAt >= now.AddDays(-7)),
                "month" => filteredReviews.Where(r => r.CreatedAt >= now.AddMonths(-1)),
                "quarter" => filteredReviews.Where(r => r.CreatedAt >= now.AddMonths(-3)),
                _ => filteredReviews
            };
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            filteredReviews = filteredReviews.Where(r => 
                r.ProductName.ToLower().Contains(searchTerm) ||
                r.UserName.ToLower().Contains(searchTerm) ||
                r.SellerName.ToLower().Contains(searchTerm) ||
                r.Title.ToLower().Contains(searchTerm) ||
                r.Comment.ToLower().Contains(searchTerm));
        }

        var totalCount = filteredReviews.Count();
        var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
        
        var pagedReviews = filteredReviews
            .OrderByDescending(r => r.CreatedAt)
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new AdminReviewsPagedResponse
        {
            Reviews = pagedReviews,
            TotalCount = totalCount,
            TotalPages = totalPages,
            CurrentPage = request.Page,
            PageSize = request.PageSize
        };
    }
}
