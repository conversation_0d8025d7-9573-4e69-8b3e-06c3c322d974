@page "/account/home"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Models.Common
@using NafaPlace.Web.Models.Cart
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Web
@attribute [Authorize]
@inject NavigationManager NavigationManager
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage
@inject IAuthService AuthService
@inject AuthenticationStateProvider AuthStateProvider
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject IOrderService OrderService
@inject ICartService CartService
@inject IJSRuntime JSRuntime

<div class="container-fluid mt-4">
    <!-- Statistiques rapides -->
            <div class="row mb-4">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                            <h5 class="card-title">@totalOrders</h5>
                            <p class="card-text text-muted">Mes Commandes</p>
                            <a href="/account/orders" class="btn btn-sm btn-outline-primary">Voir tout</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                            <h5 class="card-title">@favoriteCount</h5>
                            <p class="card-text text-muted">Favoris</p>
                            <a href="/account/favorites" class="btn btn-sm btn-outline-danger">Voir tout</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-tags fa-2x text-success mb-2"></i>
                            <h5 class="card-title">@categories?.Count()</h5>
                            <p class="card-text text-muted">Catégories</p>
                            <a href="/catalog" class="btn btn-sm btn-outline-success">Explorer</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h5 class="card-title">@featuredProducts?.Count()</h5>
                            <p class="card-text text-muted">Offres Spéciales</p>
                            <a href="/promotions" class="btn btn-sm btn-outline-warning">Découvrir</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégories populaires -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="fas fa-th-large me-2"></i>Catégories Populaires
                    </h4>
                    <div class="row">
                        @if (categories != null && categories.Any())
                        {
                            @foreach (var category in categories.Take(4))
                            {
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card h-100 border-0 shadow-sm category-card" @onclick="() => NavigateToCategory(category.Id)">
                                        <div class="card-body text-center">
                                            <i class="fas fa-cube fa-3x text-primary mb-3"></i>
                                            <h6 class="card-title">@category.Name</h6>
                                            <p class="card-text text-muted small">@category.Description</p>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="col-12 text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Produits en vedette -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="fas fa-star me-2"></i>Offres du Jour
                    </h4>
                    <div class="row">
                        @if (featuredProducts != null && featuredProducts.Any())
                        {
                            @foreach (var product in featuredProducts.Take(4))
                            {
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card h-100 border-0 shadow-sm product-card">
                                        <div class="position-relative">
                                            <a href="/catalog/products/@product.Id">
                                                @if (!string.IsNullOrEmpty(product.MainImageUrl))
                                                {
                                                    <img src="@product.MainImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                                        <i class="fas fa-image fa-3x text-muted"></i>
                                                    </div>
                                                }
                                            </a>
                                            <span class="badge bg-danger position-absolute top-0 end-0 m-2">Offre</span>
                                        </div>
                                        <div class="card-body">
                                            <h6 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h6>
                                            <p class="card-text text-muted small">@(product.Description?.Length > 50 ? product.Description.Substring(0, 50) + "..." : product.Description)</p>
                                            <div class="mb-2">
                                                <span class="h6 text-primary mb-0">@product.Price.ToString("N0") GNF</span>
                                                <small class="text-muted d-block">Stock: @product.Stock</small>
                                            </div>
                                            <div class="d-flex">
                                                <button class="btn btn-primary me-2" @onclick="() => AddToCart(product.Id)">
                                                    <i class="fas fa-shopping-cart me-1"></i> Ajouter
                                                </button>
                                                <button class="btn btn-outline-secondary" @onclick="() => AddToWishlist(product.Id)">
                                                    <i class="far fa-heart"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="col-12 text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Nouveaux produits -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="fas fa-sparkles me-2"></i>Nouveaux Arrivages
                    </h4>
                    <div class="row">
                        @if (newProducts != null && newProducts.Any())
                        {
                            @foreach (var product in newProducts.Take(4))
                            {
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card h-100 border-0 shadow-sm product-card">
                                        <div class="position-relative">
                                            <a href="/catalog/products/@product.Id">
                                                @if (!string.IsNullOrEmpty(product.MainImageUrl))
                                                {
                                                    <img src="@product.MainImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                                        <i class="fas fa-image fa-3x text-muted"></i>
                                                    </div>
                                                }
                                            </a>
                                            <span class="badge bg-success position-absolute top-0 end-0 m-2">Nouveau</span>
                                        </div>
                                        <div class="card-body">
                                            <h6 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h6>
                                            <p class="card-text text-muted small">@(product.Description?.Length > 50 ? product.Description.Substring(0, 50) + "..." : product.Description)</p>
                                            <div class="mb-2">
                                                <span class="h6 text-primary mb-0">@product.Price.ToString("N0") GNF</span>
                                                <small class="text-muted d-block">Stock: @product.Stock</small>
                                            </div>
                                            <div class="d-flex">
                                                <button class="btn btn-primary me-2" @onclick="() => AddToCart(product.Id)" @onclick:stopPropagation="true">
                                                    <i class="fas fa-shopping-cart me-1"></i> Ajouter
                                                </button>
                                                <button class="btn btn-outline-secondary" @onclick="() => AddToWishlist(product.Id)" @onclick:stopPropagation="true">
                                                    <i class="far fa-heart"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="col-12 text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
</div>

<style>
    .category-card:hover, .product-card:hover {
        transform: translateY(-5px);
        transition: transform 0.3s ease;
        cursor: pointer;
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
</style>

@code {
    private IEnumerable<CategoryDto>? categories;
    private IEnumerable<ProductDto>? featuredProducts;
    private IEnumerable<ProductDto>? newProducts;
    private int totalOrders = 0;
    private int favoriteCount = 0;
    private string? _userId;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity == null || !user.Identity.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/auth/login");
            return;
        }

        // Récupérer l'ID utilisateur
        _userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            // Charger les données en parallèle
            var categoriesTask = CategoryService.GetAllCategoriesAsync();
            var featuredTask = ProductService.GetFeaturedProductsAsync(4);
            var newTask = ProductService.GetNewProductsAsync(4);

            await Task.WhenAll(categoriesTask, featuredTask, newTask);

            categories = await categoriesTask;
            featuredProducts = await featuredTask;
            newProducts = await newTask;

            // Simuler des statistiques (à remplacer par de vraies données)
            totalOrders = 5;
            favoriteCount = 12;

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des données: {ex.Message}");
        }
    }

    private async Task Logout()
    {
        await AuthService.Logout();
        NavigationManager.NavigateTo("/", true);
    }

    private async Task NavigateToCategory(int categoryId)
    {
        NavigationManager.NavigateTo($"/catalog?categoryId={categoryId}");
        await Task.CompletedTask;
    }



    private async Task AddToCart(int productId)
    {
        Console.WriteLine($"🔍 DEBUG SecuredHome: Tentative d'ajout au panier - ProductId: {productId}");

        if (string.IsNullOrEmpty(_userId))
        {
            Console.WriteLine("❌ DEBUG SecuredHome: Utilisateur non connecté, redirection vers login");
            NavigationManager.NavigateTo("login");
            return;
        }

        try
        {
            Console.WriteLine($"🛒 DEBUG SecuredHome: Création de l'item panier - ProductId: {productId}, Quantity: 1");
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };

            Console.WriteLine($"📡 DEBUG SecuredHome: Appel API AddItemToCartAsync...");
            var result = await CartService.AddItemToCartAsync(_userId, cartItem);

            Console.WriteLine($"✅ DEBUG SecuredHome: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

            // Notification de succès
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
            Console.WriteLine($"Produit {productId} ajouté au panier.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG SecuredHome: Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private void AddToWishlist(int productId)
    {
        // TODO: Implémenter l'ajout aux favoris
        Console.WriteLine($"Ajout aux favoris: {productId}");
    }
}
