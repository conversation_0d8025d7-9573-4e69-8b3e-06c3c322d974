@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Routing
@inject NavigationManager NavigationManager

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">NafaPlace Admin</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="nav flex-column">
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive(""))" href="">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Accueil
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("dashboard"))" href="dashboard">
                <span class="fas fa-tachometer-alt" aria-hidden="true"></span> Tableau de bord
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("users"))" href="users">
                <span class="fas fa-users" aria-hidden="true"></span> Utilisateurs
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("roles"))" href="roles">
                <span class="fas fa-user-tag" aria-hidden="true"></span> Rôles
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("products"))" href="products">
                <span class="fas fa-box" aria-hidden="true"></span> Produits
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("categories"))" href="categories">
                <span class="fas fa-tags" aria-hidden="true"></span> Catégories
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("orders"))" href="orders">
                <span class="fas fa-shopping-cart" aria-hidden="true"></span> Commandes
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("reviews"))" href="reviews">
                <span class="fas fa-star" aria-hidden="true"></span> Avis
            </a>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
    
    private string GetActive(string href)
    {
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;
        
        if (string.IsNullOrEmpty(href) && path == "/")
            return "active";
            
        if (!string.IsNullOrEmpty(href) && path.StartsWith($"/{href}"))
            return "active";
            
        return "";
    }
}
