@page "/dashboard"
@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Models.Common
@using NafaPlace.AdminPortal.Services
@inject IOrderService OrderService
@inject IUserService UserService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Dashboard - NafaPlace Admin</PageTitle>

<div class="container-fluid px-4">
    <h1 class="mt-4">Tableau de bord</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item active">Tableau de bord</li>
    </ol>
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h4>Utilisateurs</h4>
                    <h2>@(userCount?.ToString() ?? "...")</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/users">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <h4>Commandes</h4>
                    <h2>@(orderStats?.TotalOrders.ToString() ?? "...")</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/orders">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h4>Chiffre d'affaires</h4>
                    <h2>@(orderStats?.TotalRevenue.ToString("N0") ?? "...") GNF</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/orders">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <h4>Commandes en attente</h4>
                    <h2>@(orderStats?.PendingOrders.ToString() ?? "...")</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/orders">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-graph-up me-1"></i>
                    Activités récentes
                </div>
                <div class="card-body">
                    <p class="text-muted">Aucune activité récente à afficher.</p>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-bar-chart me-1"></i>
                    Informations système
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Version
                            <span class="badge bg-primary rounded-pill">1.0.0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Environnement
                            <span class="badge bg-info rounded-pill">Développement</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Dernière mise à jour
                            <span class="badge bg-secondary rounded-pill">@DateTime.Now.ToShortDateString()</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private OrderStatistics? orderStats;
    private int? userCount;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        isLoading = true;

        try
        {
            // Charger les statistiques des commandes
            var orderStatsTask = OrderService.GetOrderStatisticsAsync();

            // Charger le nombre d'utilisateurs
            var usersTask = UserService.GetUsersAsync(1, 1);

            await Task.WhenAll(orderStatsTask, usersTask);

            orderStats = await orderStatsTask;
            var usersResult = await usersTask;
            userCount = usersResult.TotalCount;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Erreur lors du chargement des données du tableau de bord: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}