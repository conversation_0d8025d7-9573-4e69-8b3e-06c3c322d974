# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/ApiGateways/Web/NafaPlace.ApiGateway/NafaPlace.ApiGateway.csproj", "ApiGateways/Web/NafaPlace.ApiGateway/"]
RUN dotnet restore "ApiGateways/Web/NafaPlace.ApiGateway/NafaPlace.ApiGateway.csproj"

# Copier le reste des fichiers et construire
COPY ["src/ApiGateways/Web/NafaPlace.ApiGateway/", "ApiGateways/Web/NafaPlace.ApiGateway/"]
WORKDIR "/src/ApiGateways/Web/NafaPlace.ApiGateway"
RUN dotnet build "NafaPlace.ApiGateway.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.ApiGateway.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.ApiGateway.dll"]
