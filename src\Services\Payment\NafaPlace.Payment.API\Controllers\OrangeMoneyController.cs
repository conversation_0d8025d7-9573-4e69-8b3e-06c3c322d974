using Microsoft.AspNetCore.Mvc;
using NafaPlace.Payment.API.Models;
using NafaPlace.Payment.API.Services;

namespace NafaPlace.Payment.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OrangeMoneyController : ControllerBase
    {
        private readonly IOrangeMoneyService _orangeMoneyService;
        private readonly ILogger<OrangeMoneyController> _logger;

        public OrangeMoneyController(
            IOrangeMoneyService orangeMoneyService,
            ILogger<OrangeMoneyController> logger)
        {
            _orangeMoneyService = orangeMoneyService;
            _logger = logger;
        }

        [HttpPost("initiate")]
        public async Task<ActionResult<OrangeMoneyPaymentResponse>> InitiatePayment([FromBody] OrangeMoneyPaymentRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.PhoneNumber) || request.Amount <= 0)
                {
                    return BadRequest(new { error = "Numéro de téléphone et montant requis" });
                }

                var result = await _orangeMoneyService.InitiatePaymentAsync(request);
                
                _logger.LogInformation("Orange Money payment initiated: {TransactionId} for order {OrderId}", 
                    result.TransactionId, request.OrderId);

                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid Orange Money payment request: {Message}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating Orange Money payment");
                return StatusCode(500, new { error = "Erreur lors de l'initiation du paiement Orange Money" });
            }
        }

        [HttpGet("status/{transactionId}")]
        public async Task<ActionResult<OrangeMoneyStatusResponse>> GetPaymentStatus(string transactionId)
        {
            try
            {
                if (string.IsNullOrEmpty(transactionId))
                {
                    return BadRequest(new { error = "ID de transaction requis" });
                }

                var result = await _orangeMoneyService.GetPaymentStatusAsync(transactionId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Orange Money payment status");
                return StatusCode(500, new { error = "Erreur lors de la vérification du statut" });
            }
        }

        [HttpPost("callback")]
        public async Task<IActionResult> HandleCallback()
        {
            try
            {
                var signature = Request.Headers["X-Orange-Signature"].FirstOrDefault();
                var payload = await new StreamReader(Request.Body).ReadToEndAsync();

                if (string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(payload))
                {
                    _logger.LogWarning("Orange Money callback received without signature or payload");
                    return BadRequest("Signature et payload requis");
                }

                var isValid = await _orangeMoneyService.VerifyCallbackAsync(signature, payload);
                if (!isValid)
                {
                    _logger.LogWarning("Invalid Orange Money callback signature");
                    return Unauthorized("Signature invalide");
                }

                _logger.LogInformation("Orange Money callback received: {Payload}", payload);

                // TODO: Parser le payload et mettre à jour le statut de la commande
                // Exemple de payload Orange Money:
                // {
                //   "transaction_id": "***********",
                //   "order_id": "ORDER-123",
                //   "status": "SUCCESS",
                //   "amount": 10000,
                //   "currency": "GNF",
                //   "phone": "+224621234567"
                // }

                return Ok(new { message = "Callback traité avec succès" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling Orange Money callback");
                return StatusCode(500, new { error = "Erreur lors du traitement du callback" });
            }
        }

        [HttpPost("simulate-payment")]
        public async Task<IActionResult> SimulatePayment([FromBody] SimulatePaymentRequest request)
        {
            try
            {
                // Endpoint pour simuler un paiement Orange Money en développement
                _logger.LogInformation("Simulating Orange Money payment: {TransactionId} -> {Status}", 
                    request.TransactionId, request.Status);

                // TODO: Mettre à jour le statut de la commande selon la simulation
                
                return Ok(new { 
                    message = $"Paiement simulé: {request.Status}",
                    transactionId = request.TransactionId,
                    status = request.Status
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error simulating Orange Money payment");
                return StatusCode(500, new { error = "Erreur lors de la simulation" });
            }
        }
    }

    public class SimulatePaymentRequest
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = "SUCCESS"; // SUCCESS, FAILED, CANCELLED
        public string OrderId { get; set; } = string.Empty;
    }
}
